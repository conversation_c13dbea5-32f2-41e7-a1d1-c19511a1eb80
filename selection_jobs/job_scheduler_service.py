import os
import sys
import json
import time
import threading
from datetime import datetime, timedelta
import pytz
import sqlite3
import logging
import queue
import uuid
from flask import Flask, request, jsonify
from flask_cors import CORS
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from apscheduler.triggers.date import DateTrigger
from dateutil import parser


parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from Utils.globals import determine_database_path
DATABASE = determine_database_path()

# Import custom modules
from selection_jobs.job_retry_manager import get_retry_manager
from selection_jobs.worker_pool import get_worker_pool
from selection_jobs.db_interface import update_selection_status

log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, 'scheduler_service.log')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("job_scheduler")

# Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
MAX_CONCURRENT_JOBS = 3  # Maximum number of jobs to process concurrently
IMMEDIATE_JOB_DELAY = 60  # Seconds to delay between immediate job batches
CHECK_INTERVAL = 60  # Seconds between checking for new jobs
SCHEDULE_WINDOW = 15 * 60  # 15 minutes in seconds - window to pick up scheduled jobs
MAX_RETRY_ATTEMPTS = 3  # Maximum number of retry attempts for failed jobs

# Job status constants
JOB_STATUS = {
    'CREATED': 'created',  # Initial status for new jobs
    'QUEUED': 'queued',
    'DOWNLOADING': 'downloading',
    'UPLOAD_STARTED': 'upload_started',
    'UPLOADING': 'uploading',
    'UPLOAD_COMPLETE': 'upload_complete',
    'PLAYLIST_UPDATED': 'playlist_updated',
    'FAILED': 'failed',
    'COMPLETED': 'completed',
     'PENDING': 'pending',
      'PROCESSING': 'processing',
}

# Initialize worker pool and retry manager
worker_pool = None
retry_manager = None

# Configure job scheduler
jobstores = {
    'default': SQLAlchemyJobStore(url=f'sqlite:///{DATABASE}')
}
executors = {
    'default': ThreadPoolExecutor(MAX_CONCURRENT_JOBS),
    'processpool': ProcessPoolExecutor(2)
}
job_defaults = {
    'coalesce': False,
    'max_instances': 1
}

scheduler = BackgroundScheduler(
    jobstores=jobstores,
    executors=executors,
    job_defaults=job_defaults,
    timezone=pytz.UTC
)

def get_db_connection():
    """Create a connection to the SQLite database"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def update_job_status(selection_id, status, details=None):
    """Update the status of a job in the database"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get current status JSON
        cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (selection_id,))
        row = cursor.fetchone()

        if not row:
            logger.error(f"Selection ID {selection_id} not found in database")
            return False

        # Parse existing status or create new status object
        try:
            current_status = json.loads(row['status']) if row['status'] else {}
        except json.JSONDecodeError:
            current_status = {}

        # Update status with new information
        # Always use ISO format for timestamps for consistency
        timestamp = datetime.now().isoformat()
        current_status['current'] = status
        current_status['last_updated'] = timestamp

        # Convert any existing Unix timestamp to ISO format
        if 'last_updated' in current_status and isinstance(current_status['last_updated'], (int, float)):
            # Convert Unix timestamp to ISO format
            unix_time = current_status['last_updated']
            current_status['last_updated'] = datetime.fromtimestamp(unix_time).isoformat()

        if 'history' not in current_status:
            current_status['history'] = []

        history_entry = {
            'status': status,
            'timestamp': timestamp
        }

        if details:
            history_entry['details'] = details

        current_status['history'].append(history_entry)

        # Save updated status back to database
        cursor.execute(
            "UPDATE upload_selections SET status = ? WHERE id = ?",
            (json.dumps(current_status), selection_id)
        )
        conn.commit()
        logger.info(f"Updated job {selection_id} status to {status}")
        return True

    except Exception as e:
        logger.error(f"Error updating job status: {str(e)}")
        return False

    finally:
        if conn:
            conn.close()

def process_selection_job(selection_id, selection_data):
    """Process a selection job - this is where the actual work happens"""
    # This function is now just a wrapper around the job_worker module
    # The actual processing is done in the worker pool
    from selection_jobs.job_worker import process_selection_job as worker_process_job

    try:
        # Update status to processing
        update_job_status(selection_id, JOB_STATUS['PROCESSING'],
                         {"message": "Job processing started"})

        logger.info(f"Processing job {selection_id}")

        # Call the worker module to process the job
        result = worker_process_job(selection_id, selection_data, update_job_status)

        if result:
            logger.info(f"Job {selection_id} completed successfully")
        else:
            logger.error(f"Job {selection_id} failed")

        return result

    except Exception as e:
        logger.error(f"Error processing job {selection_id}: {str(e)}")

        # Update status to failed
        update_job_status(selection_id, JOB_STATUS['FAILED'],
                         {"message": f"Job failed: {str(e)}"})

        return False

def job_executor(selection_id, selection_data):
    """Wrapper function for the scheduler to execute jobs"""
    global worker_pool

    try:
        # Queue the job in the worker pool
        if worker_pool:
            worker_pool.queue_job(selection_id, selection_data, 0)  # Highest priority
            logger.info(f"Job {selection_id} queued in worker pool")
        else:
            # Fallback to direct processing if worker pool is not available
            logger.warning(f"Worker pool not available, processing job {selection_id} directly")
            process_selection_job(selection_id, selection_data)

    except Exception as e:
        logger.error(f"Error in job executor for {selection_id}: {str(e)}")
        update_job_status(selection_id, JOB_STATUS['FAILED'],
                         {"message": f"Job execution failed: {str(e)}"})

def queue_job_by_id(selection_id, priority=100):
    """Queue a job for processing by its ID"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get job data
        cursor.execute("SELECT selection FROM upload_selections WHERE id = ?", (selection_id,))
        row = cursor.fetchone()

        if not row:
            logger.error(f"Selection ID {selection_id} not found")
            return False

        selection_data = json.loads(row['selection'])

        # Queue the job
        return queue_job(selection_id, selection_data, priority)

    except Exception as e:
        logger.error(f"Error queuing job {selection_id}: {str(e)}")
        return False

    finally:
        if 'conn' in locals() and conn:
            conn.close()

def queue_job(selection_id, selection_data, priority=100):
    """Queue a job for processing"""
    global worker_pool

    # Update job status
    update_job_status(selection_id, JOB_STATUS['QUEUED'],
                     {"message": "Job queued for processing", "priority": priority})

    # Queue job in worker pool
    if worker_pool:
        return worker_pool.queue_job(selection_id, selection_data, priority)
    else:
        logger.error("Worker pool not initialized")
        return False

def process_immediate_jobs():
    """Process jobs from the immediate queue - this is now handled by the worker pool"""
    # This function is kept for backward compatibility
    # The worker pool now handles job processing
    return 0

def check_for_new_jobs():
    """Check for new jobs in the database"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get current time in UTC
        now = datetime.now(pytz.UTC)

        # Find jobs that need to be scheduled
        cursor.execute("""
            SELECT id, selection, sanatana_email, created_at
            FROM upload_selections
            WHERE json_extract(status, '$.current') IN
            ('created', 'failed', 'upload_failed', 'Youtube_uploadLimitExceeded')

        """)

        new_jobs = cursor.fetchall()
        scheduled_count = 0
        immediate_count = 0

        for job in new_jobs:
            selection_id = job['id']
            selection_json = job['selection']
            sanatana_email = job['sanatana_email']

            try:
                selection_data = json.loads(selection_json)

                # Add sanatana_email to selection_data
                selection_data['sanatana_email'] = sanatana_email

                # Get current status to check if this is a retry
                cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (selection_id,))
                status_row = cursor.fetchone()
                status_data = json.loads(status_row['status']) if status_row and status_row['status'] else {}

                # Handle Youtube upload limit exceeded
                if status_data.get('current') == 'Youtube_uploadLimitExceeded':
                    # Schedule for tomorrow at the same time
                    tomorrow = now + timedelta(days=1)

                    # Get the user's timezone or default to UTC
                    user_timezone = pytz.UTC
                    if selection_data.get('timeZone'):
                        try:
                            user_timezone = pytz.timezone(selection_data['timeZone'])
                        except Exception as e:
                            logger.warning(f"Invalid timezone {selection_data.get('timeZone')}: {str(e)}")

                    # Convert tomorrow to user's timezone for display
                    tomorrow_local = tomorrow.astimezone(user_timezone)

                    # Update status to indicate rescheduling
                    status_data['current'] = 'scheduled'
                    status_data['last_updated'] = datetime.now().isoformat()
                    status_data['scheduled_date'] = tomorrow.isoformat()

                    if 'history' not in status_data:
                        status_data['history'] = []

                    # Find the history entry with Youtube_uploadLimitExceeded status
                    for entry in status_data['history']:
                        if entry.get('status') == 'Youtube_uploadLimitExceeded':
                            # Add schedule information to this entry
                            entry['schedule'] = {
                                'date': tomorrow.isoformat(),
                                'message': f"Rescheduled for {tomorrow_local.strftime('%Y-%m-%d %H:%M:%S')} due to YouTube upload limit"
                            }

                    # Add a new history entry for the scheduling
                    status_data['history'].append({
                        'status': 'scheduled',
                        'timestamp': datetime.now().isoformat(),
                        'details': {
                            'message': f"Automatically rescheduled for {tomorrow_local.strftime('%Y-%m-%d %H:%M:%S')} due to YouTube upload limit",
                            'scheduled_date': tomorrow.isoformat()
                        }
                    })

                    # Update status in database
                    cursor.execute(
                        "UPDATE upload_selections SET status = ? WHERE id = ?",
                        (json.dumps(status_data), selection_id)
                    )
                    conn.commit()

                    # Schedule the job for tomorrow
                    job_id = f"scheduled_{selection_id}_{uuid.uuid4().hex[:8]}"
                    scheduler.add_job(
                        execute_scheduled_job,
                        args=[selection_id, selection_data],
                        trigger=DateTrigger(run_date=tomorrow),
                        id=job_id,
                        replace_existing=True
                    )

                    logger.info(f"Job {selection_id} rescheduled for {tomorrow_local.strftime('%Y-%m-%d %H:%M:%S')} due to YouTube upload limit")
                    scheduled_count += 1
                    continue

                # If this is a failed job, update retry count
                elif status_data.get('current') == 'failed':
                    retry_count = status_data.get('retry_count', 0) + 1

                    # Check if we've exceeded max retries
                    if retry_count > MAX_RETRY_ATTEMPTS:
                        logger.info(f"Job {selection_id} has exceeded maximum retry attempts ({MAX_RETRY_ATTEMPTS}). Skipping.")
                        continue

                    # Update status to indicate a retry is in progress
                    status_data['retry_count'] = retry_count
                    status_data['current'] = 'retrying'
                    status_data['last_updated'] = datetime.now().isoformat()

                    if 'history' not in status_data:
                        status_data['history'] = []

                    status_data['history'].append({
                        'status': 'retrying',
                        'timestamp': datetime.now().isoformat(),
                        'details': {
                            'message': f"Automatic retry #{retry_count} in progress",
                            'retry_count': retry_count
                        }
                    })

                    # Update status in database
                    cursor.execute(
                        "UPDATE upload_selections SET status = ? WHERE id = ?",
                        (json.dumps(status_data), selection_id)
                    )
                    conn.commit()

                    logger.info(f"Processing failed job {selection_id} - retry #{retry_count}")

                # Check if job should be processed immediately
                if selection_data.get('uploadWithinHour', False):
                    # Add to immediate queue with priority based on creation time
                    created_at = parser.parse(job['created_at'])

                    # Ensure created_at has timezone info
                    if created_at.tzinfo is None:
                        created_at = pytz.UTC.localize(created_at)

                    priority = (now - created_at).total_seconds()  # Older jobs get higher priority

                    # Queue job in worker pool
                    queue_job(selection_id, selection_data, priority)
                    immediate_count += 1

                # Check if job should be scheduled
                elif 'scheduleDate' in selection_data and selection_data['scheduleDate']:
                    schedule_date = parser.parse(selection_data['scheduleDate'])

                    # Apply timezone if specified
                    if selection_data.get('timeZone'):
                        try:
                            tz = pytz.timezone(selection_data['timeZone'])
                            if not schedule_date.tzinfo:
                                schedule_date = tz.localize(schedule_date)
                        except Exception as e:
                            logger.warning(f"Invalid timezone {selection_data.get('timeZone')}: {str(e)}")
                            # Default to UTC if timezone is invalid
                            if not schedule_date.tzinfo:
                                schedule_date = pytz.UTC.localize(schedule_date)

                    # Ensure schedule_date has timezone info
                    if schedule_date.tzinfo is None:
                        schedule_date = pytz.UTC.localize(schedule_date)

                    # Convert to UTC if needed
                    if schedule_date.tzinfo != pytz.UTC:
                        schedule_date = schedule_date.astimezone(pytz.UTC)

                    # Check if schedule is within the window
                    time_until_schedule = (schedule_date - now).total_seconds()

                    if 0 <= time_until_schedule <= SCHEDULE_WINDOW:
                        # Schedule is within the window, add to immediate queue
                        queue_job(selection_id, selection_data, 0)  # Highest priority
                        immediate_count += 1
                    elif time_until_schedule > 0:
                        # Schedule for future execution
                        job_id = f"scheduled_{selection_id}_{uuid.uuid4().hex[:8]}"
                        scheduler.add_job(
                            lambda sid=selection_id, sdata=selection_data: queue_job(sid, sdata, 0),
                            trigger=DateTrigger(run_date=schedule_date),
                            id=job_id,
                            replace_existing=True
                        )
                        update_job_status(selection_id, JOB_STATUS['PENDING'],
                                         {"message": f"Job scheduled for {schedule_date.isoformat()}"})
                        scheduled_count += 1
                    else:
                        # Schedule date is in the past, add to immediate queue
                        queue_job(selection_id, selection_data, 1)  # High priority
                        immediate_count += 1

            except json.JSONDecodeError:
                logger.error(f"Invalid JSON in selection data for job {selection_id}")
            except Exception as e:
                logger.error(f"Error processing job {selection_id}: {str(e)}")

        logger.info(f"Found {len(new_jobs)} new jobs. Scheduled: {scheduled_count}, Immediate: {immediate_count}")

    except Exception as e:
        logger.error(f"Error checking for new jobs: {str(e)}")

    finally:
        if 'conn' in locals() and conn:
            conn.close()

def scheduler_background_thread():
    """Background thread to check for new jobs and process immediate jobs"""
    while True:
        try:
            # Check for new jobs
            check_for_new_jobs()

            # Process immediate jobs
            processed = process_immediate_jobs()

            if processed > 0:
                logger.info(f"Processed {processed} immediate jobs")

            # Sleep for a bit
            time.sleep(CHECK_INTERVAL)

        except Exception as e:
            logger.error(f"Error in scheduler background thread: {str(e)}")
            time.sleep(CHECK_INTERVAL)

# API Endpoints
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    global worker_pool, retry_manager

    # Get worker pool stats
    worker_stats = worker_pool.get_stats() if worker_pool else {}

    # Get retry manager stats
    retry_stats = retry_manager.get_retry_stats() if retry_manager else {}

    return jsonify({
        'status': 'healthy',
        'worker_pool': worker_stats,
        'retry_manager': retry_stats,
        'scheduled_jobs': len(scheduler.get_jobs())
    })

@app.route('/jobs/queue', methods=['POST'])
def queue_job_endpoint():
    """Queue a job for immediate processing"""
    data = request.json

    if not data or 'selection_id' not in data:
        return jsonify({'error': 'Missing selection_id parameter'}), 400

    selection_id = data['selection_id']
    priority = data.get('priority', 0)  # Default to highest priority

    # Use the queue_job_by_id function
    result = queue_job_by_id(selection_id, priority)

    if result:
        return jsonify({'status': 'success', 'message': f'Job {selection_id} queued for processing'})
    else:
        return jsonify({'error': f'Failed to queue job {selection_id}'}), 500

@app.route('/jobs/cancel/<int:selection_id>', methods=['POST'])
def cancel_job(selection_id):
    """Cancel a queued job"""
    global worker_pool

    if not worker_pool:
        return jsonify({'error': 'Worker pool not initialized'}), 500

    # Try to cancel the job
    result = worker_pool.cancel_job(selection_id)

    if result:
        # Update job status
        update_job_status(selection_id, 'cancelled',
                         {"message": "Job cancelled by user"})

        return jsonify({'status': 'success', 'message': f'Job {selection_id} cancelled'})
    else:
        # Check if job is already being processed
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (selection_id,))
        row = cursor.fetchone()
        conn.close()

        if not row:
            return jsonify({'error': f'Selection ID {selection_id} not found'}), 404

        status_data = json.loads(row['status']) if row['status'] else {'current': 'unknown'}
        current_status = status_data.get('current', 'unknown')

        if current_status in ['processing', 'downloading', 'upload_started']:
            return jsonify({'error': f'Job {selection_id} is already being processed and cannot be cancelled'}), 400
        elif current_status in ['completed', 'failed']:
            return jsonify({'error': f'Job {selection_id} is already {current_status} and cannot be cancelled'}), 400
        else:
            return jsonify({'error': f'Job {selection_id} not found in queue'}), 404

@app.route('/jobs/retry/<int:selection_id>', methods=['POST'])
def retry_job(selection_id):
    """Retry a failed job"""
    global retry_manager

    if not retry_manager:
        return jsonify({'error': 'Retry manager not initialized'}), 500

    # Get current job status
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute("SELECT status FROM upload_selections WHERE id = ?", (selection_id,))
    row = cursor.fetchone()
    conn.close()

    if not row:
        return jsonify({'error': f'Selection ID {selection_id} not found'}), 404

    status_data = json.loads(row['status']) if row['status'] else {'current': 'unknown'}
    current_status = status_data.get('current', 'unknown')

    if current_status != 'failed':
        return jsonify({'error': f'Job {selection_id} is not failed (current status: {current_status})'}), 400

    # Get current retry count
    retry_count = status_data.get('retry_count', 0)

    # Check if max retries reached
    if retry_count >= 3:
        return jsonify({'error': f'Job {selection_id} has already been retried {retry_count} times (maximum)'}), 400

    # Queue the job for retry
    result = queue_job_by_id(selection_id, 0)  # Highest priority

    if result:
        # Update retry count
        status_data['retry_count'] = retry_count + 1
        status_data['current'] = 'retry_queued'

        # Add to history
        timestamp = datetime.now().isoformat()
        if 'history' not in status_data:
            status_data['history'] = []

        status_data['history'].append({
            'status': 'retry_queued',
            'timestamp': timestamp,
            'details': {
                'message': f"Manual retry #{retry_count + 1} queued",
                'retry_count': retry_count + 1
            }
        })

        # Update status in database
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE upload_selections SET status = ? WHERE id = ?",
            (json.dumps(status_data), selection_id)
        )
        conn.commit()
        conn.close()

        return jsonify({
            'status': 'success',
            'message': f'Job {selection_id} queued for retry (attempt {retry_count + 1})'
        })
    else:
        return jsonify({'error': f'Failed to queue job {selection_id} for retry'}), 500

@app.route('/jobs/status/<int:selection_id>', methods=['GET'])
def get_job_status(selection_id):
    """Get the status of a job"""
    global worker_pool

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get job status
        cursor.execute("SELECT selection, status FROM upload_selections WHERE id = ?", (selection_id,))
        row = cursor.fetchone()

        if not row:
            return jsonify({'error': f'Selection ID {selection_id} not found'}), 404

        status_data = json.loads(row['status']) if row['status'] else {'current': 'unknown'}
        selection_data = json.loads(row['selection'])

        # Get worker pool stats
        if worker_pool:
            worker_stats = worker_pool.get_stats()

            # Check if job is active
            is_active = selection_id in worker_stats['jobs']['active_ids']

            # Check if job is in queue (this would require worker pool to expose this info)
            in_queue = False
            queue_position = None

            # Add additional information
            status_data['is_active'] = is_active
            status_data['in_queue'] = in_queue
            if queue_position:
                status_data['queue_position'] = queue_position

        # Add selection info
        status_data['selection'] = {
            'source': selection_data.get('source'),
            'title': selection_data.get('title'),
            'videoLink': selection_data.get('videoLink'),
            'scheduleDate': selection_data.get('scheduleDate'),
            'uploadWithinHour': selection_data.get('uploadWithinHour', False),
            'destinations': selection_data.get('destinations', [])
        }

        return jsonify(status_data)

    except Exception as e:
        logger.error(f"Error getting job status for {selection_id}: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if 'conn' in locals() and conn:
            conn.close()

@app.route('/jobs/list', methods=['GET'])
def list_jobs():
    """List all jobs with their statuses"""
    try:
        sanatana_email = request.args.get('sanatana_email')
        status = request.args.get('status')
        limit = request.args.get('limit', 100, type=int)

        conn = get_db_connection()
        cursor = conn.cursor()

        # Build query
        query = "SELECT id, selection, status, sanatana_email, created_at FROM upload_selections"
        params = []

        where_clauses = []
        if sanatana_email:
            where_clauses.append("sanatana_email = ?")
            params.append(sanatana_email)

        if status:
            where_clauses.append("status LIKE ?")
            params.append(f'%"current":"{status}"%')

        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        query += " ORDER BY created_at DESC LIMIT ?"
        params.append(limit)

        # Execute query
        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Format results
        jobs = []
        for row in rows:
            status_data = json.loads(row['status']) if row['status'] else {'current': 'unknown'}
            selection_data = json.loads(row['selection'])

            job_info = {
                'id': row['id'],
                'sanatana_email': row['sanatana_email'],
                'created_at': row['created_at'],
                'status': status_data.get('current', 'unknown'),
                'last_updated': status_data.get('last_updated'),
                'source': selection_data.get('source'),
                'title': selection_data.get('title'),
                'schedule_date': selection_data.get('scheduleDate')
            }

            jobs.append(job_info)

        return jsonify(jobs)

    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        return jsonify({'error': str(e)}), 500

    finally:
        if conn:
            conn.close()

# Function to retry a failed job
def retry_failed_job(selection_id):
    """Retry a failed job"""
    logger.info(f"Retrying failed job {selection_id}")
    return queue_job_by_id(selection_id)

def start_scheduler():
    """Start the scheduler, worker pool, and retry manager"""
    global worker_pool, retry_manager

    # Start the scheduler
    scheduler.start()
    logger.info("Job scheduler started")

    # Initialize worker pool
    worker_pool = get_worker_pool(
        min_workers=2,
        max_workers=MAX_CONCURRENT_JOBS,
        cpu_threshold=70,
        memory_threshold=80
    )

    # Set up worker pool callback
    worker_pool._update_job_status = update_job_status
    logger.info("Worker pool initialized")

    # Initialize retry manager
    retry_manager = get_retry_manager(
        scheduler=scheduler,
        queue_job_callback=retry_failed_job
    )
    logger.info("Retry manager initialized")

    # Start background thread
    thread = threading.Thread(target=scheduler_background_thread, daemon=True)
    thread.start()
    logger.info("Background thread started")

if __name__ == "__main__":
    import argparse, sys
    # Service name
    service_name = "JOB_SCHEDULER"

    # Parse command-line arguments for port
    arg_parser = argparse.ArgumentParser(description=f"Start the {service_name} microservice.")
    arg_parser.add_argument('--port', type=int, help="Port to run the service on")
    args = arg_parser.parse_args(sys.argv[1:])

    # Determine the port: Use CLI argument > Environment variable > Default
    PORT = args.port or int(os.getenv(service_name, 5007))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)

    from Utils.globals import save_port_in_global_ports

    # Write back to global ports file
    save_port_in_global_ports(service_name, PORT)

    # Start scheduler
    start_scheduler()

    print(f"Starting service {service_name} on port {PORT}...")
    # Start Flask app
    app.run(host='0.0.0.0', port=PORT, debug=False, threaded=True)

# Function to execute a scheduled job
def execute_scheduled_job(selection_id, selection_data):
    """Execute a job that was scheduled for a specific time"""
    logger.info(f"Executing scheduled job {selection_id}")
    queue_job(selection_id, selection_data, 0)  # Highest priority


