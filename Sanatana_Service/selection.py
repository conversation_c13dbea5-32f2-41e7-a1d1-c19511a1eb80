
from flask import Flask, request, jsonify
from root_app import app
from db import DATABASE

import os
import sqlite3
import requests
import time
import json
import urllib.parse
import datetime


# Add this endpoint to handle upload selection submissions
@app.route("/upload_selection", methods=["POST"])
def upload_selection():
    """
    Store upload selection data in the database.

    Expected JSON payload:
    {
        "sanatana_email": "<EMAIL>",
        "selection": "JSON string of the selection data"
    }

    Returns:
    {
        "id": row_id,
        "message": "Upload selection stored successfully"
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ["sanatana_email", "selection"]
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400

        sanatana_email = data["sanatana_email"]
        selection = data["selection"]

        # Create status JSON with current timestamp in ISO format
        current_timestamp = datetime.datetime.now().isoformat()
        status_json = json.dumps(
            {"last_updated": current_timestamp, "current": "created", "history": [
                {"status": "created", "timestamp": current_timestamp}
            ]}
        )

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Insert new record
        cursor.execute(
            """
            INSERT INTO upload_selections
            (sanatana_email, selection, status)
            VALUES (?, ?, ?)
            """,
            (sanatana_email, selection, status_json),
        )

        row_id = cursor.lastrowid
        conn.commit()

        return jsonify(
            {"id": row_id, "message": "Upload selection stored successfully"}
        ), 200

    except Exception as e:
        print(f"Error storing upload selection: {str(e)}")
        return jsonify({"error": str(e)}), 500

    finally:
        if "conn" in locals():
            conn.close()


# Add this endpoint to get upload selection information
@app.route("/get_upload_selection/<int:selection_id>", methods=["GET"])
def get_upload_selection(selection_id):
    """
    Get upload selection information from the database.

    Returns:
    {
        "id": row_id,
        "sanatana_email": "<EMAIL>",
        "selection": "JSON string of the selection data",
        "status": "JSON string of status history",
        "created_at": timestamp,
        "updated_at": timestamp
    }
    """
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT id, sanatana_email, selection, status,
                   created_at, updated_at
            FROM upload_selections
            WHERE id = ?
            """,
            (selection_id,),
        )

        row = cursor.fetchone()

        if not row:
            return jsonify({"error": "Upload selection not found"}), 404

        selection_info = {
            "id": row[0],
            "sanatana_email": row[1],
            "selection": row[2],
            "status": row[3],
            "created_at": row[4],
            "updated_at": row[5],
        }

        return jsonify(selection_info), 200

    except Exception as e:
        print(f"Error retrieving upload selection: {str(e)}")
        return jsonify({"error": str(e)}), 500

    finally:
        if "conn" in locals():
            conn.close()


# Add this endpoint to update upload selection status
@app.route("/update_upload_selection_status/<int:selection_id>", methods=["POST"])
def update_upload_selection_status(selection_id):
    """
    Update the status of an upload selection.

    Expected JSON payload:
    {
        "status": "new status"
    }

    Returns:
    {
        "id": row_id,
        "message": "Upload selection status updated successfully"
    }
    """
    try:
        data = request.get_json()

        # Validate required fields
        if "status" not in data:
            return jsonify({"error": "Missing required field: status"}), 400

        new_status = data["status"]
        current_timestamp = int(time.time())

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Get current status
        cursor.execute(
            "SELECT status FROM upload_selections WHERE id = ?", (selection_id,)
        )

        row = cursor.fetchone()
        if not row:
            return jsonify({"error": "Upload selection not found"}), 404

        # Parse current status JSON
        current_status = json.loads(row[0])

        # Add new status entry
        current_status.append(
            {"uploaded_time": current_timestamp, "status": new_status}
        )

        # Update status
        cursor.execute(
            """
            UPDATE upload_selections
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """,
            (json.dumps(current_status), selection_id),
        )

        conn.commit()

        return jsonify(
            {
                "id": selection_id,
                "message": "Upload selection status updated successfully",
            }
        ), 200

    except Exception as e:
        print(f"Error updating upload selection status: {str(e)}")
        return jsonify({"error": str(e)}), 500

    finally:
        if "conn" in locals():
            conn.close()


# Add this endpoint to get all upload selections for a user
@app.route("/get_user_upload_selections", methods=["GET"])
def get_user_upload_selections():
    """
    Get all upload selections for a user.

    Query parameters:
    - sanatana_email: Email of the user
    - limit: Maximum number of records to return (default: 100)
    - offset: Number of records to skip (default: 0)

    Returns:
    {
        "selections": [
            {
                "id": row_id,
                "sanatana_email": "<EMAIL>",
                "selection": "JSON string of the selection data",
                "status": "JSON string of status history",
                "created_at": timestamp,
                "updated_at": timestamp
            },
            ...
        ],
        "total": total_count
    }
    """
    try:
        sanatana_email = request.args.get("sanatana_email")
        limit = int(request.args.get("limit", 100))
        offset = int(request.args.get("offset", 0))

        if not sanatana_email:
            return jsonify({"error": "sanatana_email parameter is required"}), 400

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()

        # Get total count
        cursor.execute(
            "SELECT COUNT(*) FROM upload_selections WHERE sanatana_email = ?",
            (sanatana_email,),
        )
        total_count = cursor.fetchone()[0]

        # Get selections with pagination
        cursor.execute(
            """
            SELECT id, sanatana_email, selection, status,
                   created_at, updated_at
            FROM upload_selections
            WHERE sanatana_email = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
            """,
            (sanatana_email, limit, offset),
        )

        rows = cursor.fetchall()

        selections = []
        for row in rows:
            selections.append(
                {
                    "id": row[0],
                    "sanatana_email": row[1],
                    "selection": row[2],
                    "status": row[3],
                    "created_at": row[4],
                    "updated_at": row[5],
                }
            )

        return jsonify({"selections": selections, "total": total_count}), 200

    except Exception as e:
        print(f"Error retrieving user upload selections: {str(e)}")
        return jsonify({"error": str(e)}), 500

    finally:
        if "conn" in locals():
            conn.close()