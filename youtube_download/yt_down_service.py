from flask import Flask, request, Response
from flask_cors import CORS
import os
import tempfile
import uuid
import shutil
import re
import json
from yt_down import get_video_info, get_video, get_audio

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Ensure Flask handles JSON with UTF-8 encoding
app.config['JSON_AS_ASCII'] = False
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = False

# Custom JSON response function to ensure proper UTF-8 encoding
def json_response(data, status=200):
    return Response(
        json.dumps(data, ensure_ascii=False),
        status=status,
        mimetype='application/json; charset=utf-8'
    )

# Configuration
DOWNLOAD_FOLDER = os.environ.get('DOWNLOAD_FOLDER', os.path.join(tempfile.gettempdir(), 'youtube_downloads'))
ALLOWED_EXTENSIONS = {'mp3', 'mp4'}
MAX_CONTENT_LENGTH = 500 * 1024 * 1024  # 500 MB

# Create download folder if it doesn't exist
os.makedirs(DOWNLOAD_FOLDER, exist_ok=True)

# Function to sanitize file names while preserving Unicode characters

def sanitize_filename(filename):
    # Replace spaces with underscores
    filename = filename.replace(' ', '_')

    # Remove single and double quotes
    filename = filename.replace("'", "").replace('"', '')

    # Remove emojis and non-ascii characters
    filename = filename.encode('ascii', 'ignore').decode()

    # Keep only letters, numbers, underscores, dashes, and dots
    filename = re.sub(r'[^a-zA-Z0-9._-]', '', filename)

    # Truncate if too long
    if len(filename) > 200:
        name, ext = os.path.splitext(filename)
        filename = name[:196] + ext

    return filename

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint"""
    return json_response({
        'status': 'healthy',
        'service': 'YouTube Download Service'
    })

@app.route('/get_tutorials', methods=['GET'])
def get_tutorials():
    """Get tutorial videos from tutorials.json"""
    try:
        # Path to tutorials.json file
        tutorials_path = os.path.join(os.path.dirname(__file__), 'tutorials.json')

        # Check if file exists
        if not os.path.exists(tutorials_path):
            return json_response({'error': 'Tutorials file not found'}, 404)

        # Read the tutorials.json file
        with open(tutorials_path, 'r', encoding='utf-8') as f:
            tutorials = json.load(f)

        return json_response(tutorials)
    except Exception as e:
        return json_response({'error': str(e)}, 500)

@app.route('/video-info', methods=['GET', 'OPTIONS'])
def get_video_info_endpoint():
    if request.method == 'OPTIONS':
        return '', 200  # Or use make_response with proper headers if needed
    
    """Get video information without downloading"""
    url = request.args.get('url')

    if not url:
        return json_response({'error': 'URL parameter is required'}, 400)

    try:
        info = get_video_info(url)
        #print("get_video_info_endpoint: info:", json.dumps(info,indent=2))
        # Don't sanitize the title or description here - return them with full Unicode support

        return json_response(info)
    except Exception as e:
        #print(f"get_video_info_endpoint: Exception {e}")
        return json_response({'error': str(e)}, 500)

@app.route('/download', methods=['POST'])
def download():
    """Download video or audio from YouTube"""
    data = request.json

    
    if not data:
        print("download: error:  not data. Request body is required ")
        return json_response({'error': 'Request body is required'}, 400)

    url = data.get('url')
    media_type = data.get('type', 'video')  # Default to video
    resolution = data.get('resolution')  # Optional
    folder_path = data.get('folder_path')  # Optional custom folder path

    if not url:
        print("download: error:  no url")
        return json_response({'error': 'URL is required'}, 400)

    if media_type not in ['video', 'audio']:
        print("download: error:  no media_type")
        return json_response({'error': 'Type must be either "video" or "audio"'}, 400)

    # Validate folder_path if provided
    if folder_path and not os.path.isdir(folder_path):
        try:
            print(f"download: folder_path {folder_path} doesn't exist. Creating...")
            # Try to create the directory if it doesn't exist
            os.makedirs(folder_path, exist_ok=True)
        except Exception as e:
            return json_response({'error': f'Invalid folder path: {str(e)}'}, 400)

    #Create a unique folder for this download
    download_id = str(uuid.uuid4())
    temp_download_path = os.path.join(DOWNLOAD_FOLDER, download_id)
    os.makedirs(temp_download_path, exist_ok=True)
    #temp_download_path = folder_path
    try:
        # # Get video info first
        # title, _ = get_video_info(url)
        # # Don't sanitize the title - keep full Unicode support

        # Download the content to temporary location
        print("download: now using ytdp START ----------------------------------------")
        if media_type == 'audio':
            get_audio(url, temp_download_path)
        else:
            get_video(url, temp_download_path, resolution=resolution)
        print("download: now using ytdp END =========================================")

        # Find the downloaded file
        downloaded_files = os.listdir(temp_download_path)

        if not downloaded_files:
            print("download: error: It shows no files are there in downloaded_files")
            return json_response({'error': 'Download failed, no files found'}, 500)

        # We'll only process the first file for simplicity
        if downloaded_files:
            original_file = downloaded_files[0]  # Get the first file
            print(f"download: picking the file: {original_file}")
            # Sanitize the filename to remove problematic characters
            sanitized_file = sanitize_filename(original_file)

            # If the filename changed, rename the file
            source_path = os.path.join(temp_download_path, original_file)
            if sanitized_file != original_file:
                sanitized_path = os.path.join(temp_download_path, sanitized_file)
                os.rename(source_path, sanitized_path)
                source_path = sanitized_path

            print(f"download: source_path: {source_path}")
            
            if folder_path:
                print(f"download: moving to customr folder path: {folder_path}")
                # Move to custom folder
                dest_path = os.path.join(folder_path, sanitized_file)
                # Normalize the path to ensure proper formatting
                dest_path = os.path.normpath(dest_path)
                shutil.move(source_path, dest_path)  # Move file instead of copying
                final_path = dest_path
               
            else:
                # Keep in temp folder
                final_path = os.path.normpath(source_path)
            
            print(f"download: final_path: {final_path}")
        else:
            final_path = ""
            sanitized_file = ""

        # Return information about the download
        return json_response({
            'download_id': "dummy_doesont_matter    ",

            'type': media_type,
            'resolution': resolution,
            'file_name': sanitized_file if downloaded_files else "",
            'file_path': final_path,
            'folder_path': os.path.normpath(folder_path or temp_download_path),
            'status': 'completed'
        })
    except Exception as e:
        # Clean up on error
        print(f"download: EXCEPTION e: {str(e)}")
        return json_response({'error': str(e)}, 500)

@app.route('/download/<download_id>', methods=['GET'])
def get_download_status(download_id):
    """Get the status of a download"""
    download_path = os.path.join(DOWNLOAD_FOLDER, download_id)

    if not os.path.exists(download_path):
        return json_response({'error': 'Download not found'}, 404)

    files = os.listdir(download_path)

    # Get the first file if available and sanitize it
    if files:
        original_file = files[0]
        file_name = sanitize_filename(original_file)
        file_path = os.path.normpath(os.path.join(download_path, file_name))
    else:
        file_name = ""
        file_path = ""

    return json_response({
        'download_id': download_id,
        'file_name': file_name,
        'file_path': file_path,
        'folder_path': os.path.normpath(download_path),
        'status': 'completed' if files else 'processing'
    })

@app.route('/cleanup', methods=['POST'])
def cleanup_downloads():
    """Clean up old downloads"""
    try:
        # This is a simple implementation that removes all downloads
        # In a production environment, you might want to only remove downloads older than a certain age
        shutil.rmtree(DOWNLOAD_FOLDER)
        os.makedirs(DOWNLOAD_FOLDER, exist_ok=True)
        return json_response({'status': 'success', 'message': 'All downloads cleaned up'})
    except Exception as e:
        return json_response({'error': str(e)}, 500)

def main():
    import argparse, sys
  # Service name
    service_name = "YOUTUBE_DOWNLOAD"

    # Parse command-line arguments for port
    parser = argparse.ArgumentParser(description=f"Start the {service_name} microservice.")
    parser.add_argument('--port', type=int, help="Port to run the service on")
    args = parser.parse_args()

    # Determine the port: Use CLI argument > Environment variable > Default
    PORT = args.port or int(os.getenv(service_name, 5006))

    # Get the parent directory of the current script
    parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

    # Add it to sys.path
    sys.path.append(parent_dir)

    from Utils.globals import save_port_in_global_ports

    # Write back to global_ports.json
    save_port_in_global_ports(service_name, PORT)

    print(f"Starting service {service_name} on port {PORT}...")
    app.run(host="0.0.0.0", port=PORT, threaded=True)

if __name__ == '__main__':
    main()
    ...

    """
        usage:
        python yt_down_service.py --port 5006

        - If the port is specified in command line it will use that port
        - If not, it will look for environment variable, YOUTUBE_DOWNLOAD,
        - Even if that is not found, then it will assign default port to 5006
        - Saves port to global_ports.json under key YOUTUBE_DOWNLOAD, so local client applications can connect

    """
