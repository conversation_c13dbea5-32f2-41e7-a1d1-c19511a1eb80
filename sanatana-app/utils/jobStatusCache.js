/**
 * Utility functions for caching job status information
 * to avoid repeated API calls for completed jobs
 */

const CACHE_KEY = 'sanatana_job_status_cache';
const MAX_CACHE_SIZE = 50; // Store up to 50 job statuses

// List of statuses considered "final" (won't change anymore)
const FINAL_STATUSES = [
  'completed', 
  'failed', 
  'upload_failed', 
  'cancelled',
  'Youtube_Error'
];

/**
 * Get the job status cache from localStorage
 * @returns {Object} The cache object with job IDs as keys and status data as values
 */
export const getJobStatusCache = () => {
  if (typeof window === 'undefined') return {};
  
  try {
    const cacheString = localStorage.getItem(CACHE_KEY);
    if (!cacheString) return {};
    
    return JSON.parse(cacheString);
  } catch (error) {
    console.error('Error retrieving job status cache:', error);
    return {};
  }
};

/**
 * Save job status to localStorage cache
 * Only caches jobs with final statuses
 * @param {string} jobId - The job ID
 * @param {Object} jobInfo - The job status information
 */
export const saveJobStatusToCache = (jobId, jobInfo) => {
  if (typeof window === 'undefined' || !jobId || !jobInfo) return;
  
  // Only cache jobs with final statuses
  const currentStatus = jobInfo.status?.current || jobInfo.current;
  if (!FINAL_STATUSES.includes(currentStatus)) {
    return;
  }
  
  try {
    const cache = getJobStatusCache();
    
    // Create a new cache object with the current entry at the front
    const newCache = {
      [jobId]: {
        data: jobInfo,
        timestamp: Date.now()
      },
      ...cache
    };
    
    // Limit cache size to MAX_CACHE_SIZE entries
    const entries = Object.entries(newCache);
    if (entries.length > MAX_CACHE_SIZE) {
      const limitedEntries = entries.slice(0, MAX_CACHE_SIZE);
      const limitedCache = Object.fromEntries(limitedEntries);
      localStorage.setItem(CACHE_KEY, JSON.stringify(limitedCache));
    } else {
      localStorage.setItem(CACHE_KEY, JSON.stringify(newCache));
    }
    
    console.log(`Cached job status for job ${jobId} with status ${currentStatus}`);
  } catch (error) {
    console.error('Error saving to job status cache:', error);
  }
};

/**
 * Get job status from localStorage cache
 * @param {string} jobId - The job ID
 * @returns {Object|null} The cached job status or null if not found
 */
export const getJobStatusFromCache = (jobId) => {
  if (typeof window === 'undefined' || !jobId) return null;
  
  try {
    const cache = getJobStatusCache();
    const cachedEntry = cache[jobId];
    
    if (!cachedEntry) return null;
    
    console.log(`Retrieved cached job status for job ${jobId}`);
    return cachedEntry.data;
  } catch (error) {
    console.error('Error retrieving from job status cache:', error);
    return null;
  }
};

/**
 * Clear the entire job status cache
 */
export const clearJobStatusCache = () => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(CACHE_KEY);
  } catch (error) {
    console.error('Error clearing job status cache:', error);
  }
};

/**
 * Check if a job status is considered "final" (won't change anymore)
 * @param {string} status - The job status
 * @returns {boolean} True if the status is final
 */
export const isFinalStatus = (status) => {
  return FINAL_STATUSES.includes(status);
};
