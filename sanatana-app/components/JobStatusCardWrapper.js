import React, { useState, useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import JobStatusCard from './JobStatusCard';

/**
 * A wrapper component for JobStatusCard that provides error handling
 * and a fallback UI in case of errors
 */
const JobStatusCardWrapper = ({ jobId, onStatusChange }) => {
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Reset error state when jobId changes
  useEffect(() => {
    setHasError(false);
    setErrorMessage('');
    setIsLoading(true);
    
    // Set a timeout to hide the loading indicator after a reasonable time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);
    
    return () => clearTimeout(timer);
  }, [jobId]);

  // Error handler function
  const handleError = (error) => {
    console.error(`JobStatusCardWrapper: Error rendering job ${jobId}:`, error);
    setHasError(true);
    setErrorMessage(error.message || 'An error occurred while displaying this job');
    setIsLoading(false);
  };

  // If there's an error, show a fallback UI
  if (hasError) {
    return (
      <View className="bg-white rounded-lg border border-gray-200 my-2 p-4">
        <View className="flex-row justify-between items-center">
          <Text className="text-base font-semibold text-gray-800">Job ID: {jobId}</Text>
          <View className="bg-red-100 px-2 py-1 rounded-full">
            <Text className="text-xs text-red-600">Error</Text>
          </View>
        </View>
        <Text className="text-sm text-red-500 mt-2">{errorMessage}</Text>
      </View>
    );
  }

  // If still loading, show a loading indicator
  if (isLoading) {
    return (
      <View className="bg-white rounded-lg border border-gray-200 my-2 p-4">
        <View className="flex-row items-center">
          <ActivityIndicator size="small" color="#4F46E5" />
          <Text className="ml-2 text-sm text-gray-600">Loading job {jobId}...</Text>
        </View>
      </View>
    );
  }

  // Try to render the actual JobStatusCard, but catch any errors
  try {
    return <JobStatusCard jobId={jobId} onStatusChange={onStatusChange} />;
  } catch (error) {
    // Handle any errors that occur during rendering
    handleError(error);
    
    // Return a simple fallback UI
    return (
      <View className="bg-white rounded-lg border border-gray-200 my-2 p-4">
        <Text className="text-sm text-red-500">Failed to display job {jobId}</Text>
      </View>
    );
  }
};

export default JobStatusCardWrapper;
