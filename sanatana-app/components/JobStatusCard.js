import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, ScrollView, Animated, Linking } from 'react-native';
import { Icon } from '@rneui/themed';
import axios from 'axios';
import Constants from 'expo-constants';
import PropTypes from 'prop-types';
import { getJobStatusFromCache, saveJobStatusToCache, isFinalStatus } from '../utils/jobStatusCache';

const JOB_STATUS_COLORS = {
  created: '#9CA3AF',     // Gray
  queued: '#6366F1',     // Indigo
  downloading: '#EC4899', // Pink
  download_complete: '#34A853',
  uploading: '#8B5CF6',  // Purple
  upload_started: '#F59E0B', // Amber
  upload_complete: '#10B981', // Emerald
  playlist_updated: '#14B8A6', // Teal
  completed: '#22C55E',   // Green
  failed: '#EF4444',     // Red
  upload_failed: '#EF4424',     // Red
  retrying: '#FF9EFB',    // Light Purple
  scheduled: '#FF9EFF',   // Light Pink
  Youtube_uploadLimitExceeded: '#FF6B6B', // Bright Red
  processing: '#38BDF8',  // Sky Blue
  pending: '#A78BFA',     // Violet
  Youtube_Error: '#D93025',
  unknown: '#9CA3FF',     // Blue Gray
};

const JOB_STATUS_ICONS = {
  created: 'plus',
  queued: 'list',
  downloading: 'download',
  download_complete: 'check-circle',
  uploading: 'upload',
  upload_started: 'upload',
  upload_complete: 'check-circle',
  playlist_updated: 'list-alt',
  completed: 'check-circle',
  failed: 'exclamation-triangle',
  upload_failed: 'exclamation-triangle',
  retrying: 'repeat',
  scheduled: 'calendar',
  Youtube_uploadLimitExceeded: 'exclamation-circle',
  processing: 'cog',
  pending: 'clock',
  Youtube_Error: 'exclamation-circle',
  unknown: 'question'
};

const JobStatusCard = ({ jobId, onStatusChange }) => {
  const [jobInfo, setJobInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expanded, setExpanded] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  // Initialize animation values with error handling
  const labelOpacity = useRef(new Animated.Value(1)).current;
  const labelTranslate = useRef(new Animated.Value(0)).current;

  const toggleExpanded = () => {
    try {
      if (expanded) {
        // Animate in
        Animated.parallel([
          Animated.timing(labelOpacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(labelTranslate, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        // Animate out
        Animated.parallel([
          Animated.timing(labelOpacity, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(labelTranslate, {
            toValue: -5,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }
    } catch (animationError) {
      console.error('Animation error:', animationError);
      // Continue without animation if there's an error
    }

    setExpanded(!expanded);
  };

  const fetchJobStatus = async () => {
    try {
      setRefreshing(true);

      // First, check if we have a cached version for completed jobs
      const cachedJobInfo = getJobStatusFromCache(jobId);
      const currentJobStatus = jobInfo?.current ;

      // If we have a cached version and the current job is in a final state, use the cache
      if (cachedJobInfo && (currentJobStatus && isFinalStatus(currentJobStatus))) {
        console.log(`Using cached job status for job ${jobId} with status ${currentJobStatus}`);
        setJobInfo(cachedJobInfo);
        setError(null);
        setLoading(false);
        setRefreshing(false);
        return;
      }

      // Otherwise, fetch from the API
      const response = await axios.get(`${Constants.expoConfig.extra.SANATANA_JOBSTATUS_DOMAIN}/api/jobs/status/${jobId}`);

      // Log the raw response for debugging
      console.log(`Raw job status response for job ${jobId}:`, response.data);

      // Ensure the response has the expected structure
      const processedData = response.data;

      // Make sure status is an object
      if (typeof processedData.status === 'string') {
        processedData.status = {
          current: processedData.status,
          history: processedData.history || [],
          last_updated: processedData.last_updated || new Date().toISOString()
        };
      } else if (!processedData.status) {
        processedData.status = {
          current: 'unknown',
          history: [],
          last_updated: new Date().toISOString()
        };
      }

      // Ensure history is always an array
      if (!Array.isArray(processedData.status.history)) {
        console.warn(`Job ${jobId} history is not an array, resetting to empty array`);
        processedData.status.history = [];
      }

      // Log the history length for debugging
      console.log(`Job ${jobId} history length: ${processedData.status.history.length}`);

      setJobInfo(processedData);
      setError(null);

      // If the job is in a final state, cache it
      const jobStatus = processedData.status?.current || processedData.current;
      if (jobStatus && isFinalStatus(jobStatus)) {
        console.log(`Caching job ${jobId} with final status ${jobStatus}`);
        saveJobStatusToCache(jobId, processedData);
      }

      // Notify parent component if job info has changed
      if (onStatusChange && jobInfo) {
        // Check if status has changed or any other relevant info
        const statusChanged = jobInfo?.current !== processedData.status?.current;
        const historyChanged = JSON.stringify(jobInfo?.history) !== JSON.stringify(processedData.status?.history);
        const detailsChanged = JSON.stringify(jobInfo.selection) !== JSON.stringify(processedData.selection);

        if (statusChanged || historyChanged || detailsChanged) {
          console.log(`Job ${jobId} info changed, notifying parent`);
          onStatusChange(processedData);
        }
      }
    } catch (err) {
      console.error('Error fetching job status:', err);
      setError('Failed to load job status');

      // If we have a cached version, use it as fallback
      const cachedJobInfo = getJobStatusFromCache(jobId);
      if (cachedJobInfo) {
        console.log(`Using cached job status as fallback for job ${jobId}`);
        setJobInfo(cachedJobInfo);
        setError(null);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchJobStatus();

    // Set up polling for active jobs
    const interval = setInterval(() => {
      // Get current job status
      const currentStatus = jobInfo?.current ;

      // Don't poll for jobs with final statuses
      if (currentStatus && isFinalStatus(currentStatus)) {
        console.log(`Job ${jobId} has final status ${currentStatus}, not auto-refreshing`);
        return;
      }

      // For all other statuses, auto-refresh
      console.log(`Auto-refreshing job ${jobId} with status ${currentStatus}`);
      fetchJobStatus();
    }, 10000); // Poll every 10 seconds for active jobs

    return () => clearInterval(interval);
  }, [jobId, jobInfo?.current]); // Depend on jobId and current status

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusColor = (status) => {
    return JOB_STATUS_COLORS[status] || JOB_STATUS_COLORS.unknown;
  };

  const getStatusIcon = (status) => {
    return JOB_STATUS_ICONS[status] || JOB_STATUS_ICONS.unknown;
  };

  const renderStatusBadge = (status) => {
    try {
      // Make sure status is a string
      const statusStr = typeof status === 'string' ? status : 'unknown';
      const color = getStatusColor(statusStr);
      const iconName = getStatusIcon(statusStr);

      console.log(`Rendering status badge: ${statusStr}, color: ${color}, icon: ${iconName}`);

      return (
        <View className="flex-row items-center px-2 py-1 rounded-full" style={{ backgroundColor: color }}>
          <Icon
            name={iconName}
            type="font-awesome"
            size={12}
            color="white"
            containerStyle={{ marginRight: 4 }}
          />
          <Text className="text-white text-xs font-medium capitalize">
            {statusStr.replace(/_/g, ' ')}
          </Text>
        </View>
      );
    } catch (error) {
      console.error('Error rendering status badge:', error);
      // Return a fallback badge
      return (
        <View className="flex-row items-center px-2 py-1 rounded-full" style={{ backgroundColor: '#9CA3AF' }}>
          <Text className="text-white text-xs font-medium">unknown</Text>
        </View>
      );
    }
  };

  if (loading && !jobInfo) {
    return (
      <View className="p-4 flex-row items-center justify-center bg-white rounded-lg border border-gray-200 my-2">
        <ActivityIndicator size="small" color="#4B5563" />
        <Text className="ml-2 text-sm text-gray-600">Loading job status...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="p-4 flex-row items-center justify-center bg-red-50 rounded-lg border border-red-200 my-2">
        <Icon name="exclamation-circle" type="font-awesome" size={16} color="#EF4444" />
        <Text className="ml-2 text-sm text-red-700">{error}</Text>
      </View>
    );
  }

  if (!jobInfo) {
    return (
      <View className="p-4 flex-row items-center justify-center bg-gray-50 rounded-lg border border-gray-200 my-2">
        <Text className="text-sm text-gray-600">No job information available</Text>
      </View>
    );
  }

  // Make sure we have a valid status string
  let currentStatus = 'unknown';

  currentStatus = jobInfo?.current;

  const lastUpdated = jobInfo?.last_updated;
  const history = jobInfo?.history || [];
  const selection = jobInfo?.selection || {};

  // Detailed logging for debugging
  console.log("JOB INFO: ", jobInfo);
  console.log("Current Status: ", currentStatus);
  console.log("Status Color: ", getStatusColor(currentStatus));
  console.log("Status Icon: ", getStatusIcon(currentStatus));
  console.log("History: ", history);

  return (
    <View className="bg-white rounded-lg border border-gray-200 my-2 overflow-hidden">
      <View className="flex-row justify-between items-center p-3 border-b border-gray-200 bg-gray-50">
        <View className="flex-row items-center flex-1">
          <Text className="text-base font-semibold text-gray-800 mr-2">Job ID: {jobId}</Text>
          {/* Ensure status badge is rendered */}
          {renderStatusBadge(currentStatus)}
        </View>

        <View className="flex-row items-center">
          <TouchableOpacity
            onPress={fetchJobStatus}
            className="p-2 ml-1"
            disabled={refreshing}
          >
            {refreshing ? (
              <ActivityIndicator size="small" color="#4B5563" />
            ) : (
              <Icon name="refresh" type="font-awesome" size={16} color="#4B5563" />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={toggleExpanded}
            className="flex-row items-center p-2 ml-1 space-x-1"
          >
            {!expanded && (
              <Animated.View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  opacity: labelOpacity,
                  transform: [{ translateY: labelTranslate }],
                }}
              >
                <Text className="text-gray-600 text-sm mr-1">Details</Text>
                <Icon
                  name="info-circle"
                  type="font-awesome"
                  size={14}
                  color="#4B5563"
                />
              </Animated.View>
            )}
            <Icon
              name={expanded ? 'chevron-up' : 'chevron-down'}
              type="font-awesome"
              size={16}
              color="#4B5563"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View className="p-3">
        <View className="flex-row mb-1.5">
          <Text className="text-sm font-medium text-gray-600 w-[100px]">Source:</Text>
          {/* <Text className="text-sm text-gray-800 flex-1">{selection.source || 'Unknown'}</Text> */}
          {selection.videoLink && (

            <Text className="text-sm text-blue-600 underline">{selection.videoLink}</Text>

          )}
        </View>

        <View className="flex-row mb-1.5">
          <Text className="text-sm font-medium text-gray-600 w-[100px]">Title:</Text>
          <Text className="text-sm text-gray-800 flex-1" numberOfLines={1}>
            {selection.title || 'Using source title'}
          </Text>
        </View>

        <View className="flex-row mb-1.5">
          <Text className="text-sm font-medium text-gray-600 w-[100px]">Destination Channels:</Text>
          <Text className="text-sm text-gray-800 flex-1">
            {selection.destinations?.map(d => d.playlist.channel_name).join(', ') || 'None'}
          </Text>
        </View>

        <View className="flex-row mb-1.5">
          <Text className="text-sm font-medium text-gray-600 w-[100px]">Last Updated:</Text>
          <Text className="text-sm text-gray-800 flex-1">{formatDate(lastUpdated)}</Text>
        </View>
      </View>

      {expanded && (
        <View className="p-3 border-t border-gray-200 bg-gray-50">
          <Text className="text-[15px] font-semibold text-gray-700 mb-2">Status History</Text>

          {Array.isArray(history) && history.length > 0 ? (
            <ScrollView className="max-h-[200px]">
              {history.slice().reverse().map((entry, i) => {
                // Make sure entry has a status property
                const status = entry?.status || 'unknown';
                return (
                  <View key={`history-${i}-${entry?.timestamp || i}`} className="mb-2 p-2 bg-white rounded border border-gray-200">
                    <View className="flex-row justify-between items-center mb-1">
                      {renderStatusBadge(status)}
                      <Text className="text-xs text-gray-500">
                        {formatDate(entry?.timestamp)}
                      </Text>
                    </View>

                    {entry?.details && (
                      <View className="mt-1">
                        <Text className="text-xs text-gray-600">
                          {typeof entry.details === 'object'
                            ? (entry.details.message || JSON.stringify(entry.details))
                            : String(entry.details)
                          }
                        </Text>

                        {/* Display YouTube links if available from results */}
                        {entry?.details?.results && Object.keys(entry.details.results).length > 0 && (
                          <View className="mt-2 p-2 bg-gray-100 rounded-md">
                            <Text className="text-xs font-medium text-gray-700 mb-1">YouTube Links:</Text>
                            {Object.entries(entry.details.results).map(([destId, result], idx) => {
                              // Only show YouTube destinations
                              if (!destId.startsWith('youtube')) return null;

                              return (
                                <View key={`${destId}-${idx}`} className="mb-2">
                                  <Text className="text-xs font-medium text-gray-600">
                                    {result.title || 'YouTube Video'}
                                  </Text>

                                  {result.youtube_link && (
                                    <TouchableOpacity
                                      onPress={() => Linking.openURL(result.youtube_link)}
                                      className="mt-1 flex-row items-center"
                                    >
                                      <Icon name="youtube-play" type="font-awesome" size={14} color="#FF0000" />
                                      <Text className="text-xs text-blue-600 underline ml-1">
                                        Watch Video
                                      </Text>
                                    </TouchableOpacity>
                                  )}

                                  {result.playlist_link && (
                                    <TouchableOpacity
                                      onPress={() => Linking.openURL(result.playlist_link)}
                                      className="mt-1 flex-row items-center"
                                    >
                                      <Icon name="list" type="font-awesome" size={12} color="#065F46" />
                                      <Text className="text-xs text-blue-600 underline ml-1">
                                        Open in Playlist
                                      </Text>
                                    </TouchableOpacity>
                                  )}
                                </View>
                              );
                            })}
                          </View>
                        )}
                      </View>
                    )}
                  </View>
                );
              })}
            </ScrollView>
          ) : (
            <Text className="text-sm text-gray-500 italic text-center py-2">No history available</Text>
          )}

          {selection.videoLink && (
            <View className="mt-4">
              <Text className="text-[15px] font-semibold text-gray-700 mb-2">Source Video</Text>
              <TouchableOpacity onPress={() => Linking.openURL(selection.videoLink)}>
                <Text className="text-sm text-blue-600 underline">{selection.videoLink}</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* YouTube Links Section - Show for completed uploads */}
          {history?.some(entry =>
            (entry.status === 'upload_complete' || entry.status === 'completed') &&
            entry.details?.results &&
            Object.keys(entry.details.results).some(key => key.startsWith('youtube'))
          ) && (
            <View className="mt-4 bg-white rounded-lg p-3 border border-gray-200">
              <Text className="text-[15px] font-semibold text-gray-700 mb-2">YouTube Links</Text>

              {history
                .filter(entry =>
                  (entry.status === 'upload_complete' || entry.status === 'completed') &&
                  entry.details?.results
                )
                .map((entry, entryIndex) => (
                  <View key={`youtube-links-${entry.timestamp || entryIndex}`}>
                    {Object.entries(entry.details.results).map(([destId, result], idx) => {
                      // Only show YouTube destinations
                      if (!destId.startsWith('youtube')) return null;

                      return (
                        <View key={`${destId}-${idx}`} className="mb-3 pb-3 border-b border-gray-100">
                          <Text className="text-sm font-medium text-gray-700 mb-1">
                            {result.title || 'YouTube Video'}
                          </Text>

                          {result.youtube_link && (
                            <TouchableOpacity
                              onPress={() => Linking.openURL(result.youtube_link)}
                              className="mt-1 flex-row items-center"
                            >
                              <Icon name="youtube-play" type="font-awesome" size={16} color="#FF0000" />
                              <Text className="text-sm text-blue-600 underline ml-1">
                                Watch Video
                              </Text>
                            </TouchableOpacity>
                          )}

                          {result.playlist_link && (
                            <TouchableOpacity
                              onPress={() => Linking.openURL(result.playlist_link)}
                              className="mt-2 flex-row items-center"
                            >
                              <Icon name="list" type="font-awesome" size={14} color="#065F46" />
                              <Text className="text-sm text-blue-600 underline ml-1">
                                Open in Playlist
                              </Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      );
                    })}
                  </View>
                ))
              }
            </View>
          )}

          {selection.scheduleDate && (
            <View className="mt-4">
              <Text className="text-[15px] font-semibold text-gray-700 mb-2">Schedule</Text>
              <Text className="text-sm text-gray-600 mb-1">
                Scheduled for: {formatDate(selection.scheduleDate)}
              </Text>
              <Text className="text-sm text-gray-600">
                Upload within hour: {selection.uploadWithinHour ? 'Yes' : 'No'}
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

JobStatusCard.propTypes = {
  jobId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onStatusChange: PropTypes.func
};

JobStatusCard.defaultProps = {
  onStatusChange: null
};

export default JobStatusCard;
