import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, ScrollView, ActivityIndicator } from "react-native";
import { Icon } from "@rneui/themed";
import PropTypes from "prop-types";
import Constants from "expo-constants";
import { getFromYoutubeCache, saveToYoutubeCache } from "../../utils/youtubeCache";

// License descriptions for display
const LICENSE_DESCRIPTIONS = {
  "publicdomain": "Sometimes found in Vimeo/Internet Archive sources. Means fully free.",
  "youtube": "Standard YouTube license – most common. Not reusable without permission.",
  "creativeCommon": "Creative Commons Attribution (CC-BY). Reusable with attribution.",
  "cc-by": "Explicit Creative Commons (by attribution).",
  "cc-by-sa": "Creative Commons Share-Alike",
  "cc-by-nd": "No derivatives allowed.",
  "cc-by-nc": "Non-commercial use only.",
  "cc0": "Creative Commons Zero (true public domain)",
  "gfdl": "GNU Free Documentation License",
  "custom": "Site-defined or video-specific custom license",
  "unknown": "Not clearly defined.",
  "": "Not clearly defined."
};

export default function YouTubeVideoInfoCard({
  videoUrl,
  onCancel,
  onSelectVideo,
  buttonContainerStyle,
}) {
  const [videoInfo, setVideoInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    const fetchVideoInfo = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if we have cached data for this URL
        const cachedData = getFromYoutubeCache(videoUrl);
        if (cachedData) {
          console.log("Using cached YouTube video info for:", videoUrl);
          setVideoInfo(cachedData);
          setLoading(false);
          return;
        }

        // If no cached data, fetch from API
        console.log("Fetching YouTube video info for:", videoUrl);
        const ytdownDomain = Constants.expoConfig.extra.SANATANA_YTDOWN_DOMAIN;
        let endpoint = `${ytdownDomain}/video-info?url=${encodeURIComponent(videoUrl)}`

        // Add cache headers to the request
        const response = await fetch(endpoint, {
          headers: {
            'Cache-Control': 'max-age=3600',
            'Pragma': 'cache',
          },
          // Use cache-first strategy
          cache: 'force-cache',
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch video info: ${response.status}`);
        }

        const data = await response.json();

        // Save the fetched data to cache
        saveToYoutubeCache(videoUrl, data);

        setVideoInfo(data);
      } catch (err) {
        console.error("Error fetching video info:", err);
        setError(err.message || "Failed to fetch video information");
      } finally {
        setLoading(false);
      }
    };

    if (videoUrl) {
      fetchVideoInfo();
    }
  }, [videoUrl]);

  // Format duration from seconds to hours, minutes, seconds
  const formatDuration = (seconds) => {
    if (!seconds) return "Unknown duration";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    let formattedDuration = "";
    if (hours > 0) {
      formattedDuration += `${hours} hour${hours > 1 ? 's' : ''} `;
    }
    if (minutes > 0 || hours > 0) {
      formattedDuration += `${minutes} minute${minutes > 1 ? 's' : ''} `;
    }
    formattedDuration += `${remainingSeconds} second${remainingSeconds > 1 ? 's' : ''}`;

    return formattedDuration;
  };

  // Check if video is longer than 1 hour
  const isLongVideo = videoInfo?.duration > 3600;

  // Get license description
  const getLicenseDescription = (license) => {
    return LICENSE_DESCRIPTIONS[license] || LICENSE_DESCRIPTIONS["unknown"];
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" color="#3B82F6" />
        <Text className="mt-4 text-gray-600">Fetching video information...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center bg-white p-5">
        <Icon name="error" type="material" size={48} color="#EF4444" />
        <Text className="mt-4 text-lg font-medium text-gray-800">Error Loading Video</Text>
        <Text className="mt-2 text-gray-600 text-center">{error}</Text>
        <TouchableOpacity
          onPress={onCancel}
          className="mt-6 bg-gray-200 py-3 px-6 rounded-lg"
        >
          <Text className="text-gray-800 font-medium">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!videoInfo) {
    return (
      <View className="flex-1 justify-center items-center bg-white p-5">
        <Icon name="warning" type="material" size={48} color="#F59E0B" />
        <Text className="mt-4 text-lg font-medium text-gray-800">No Video Information</Text>
        <Text className="mt-2 text-gray-600 text-center">
          Could not retrieve information for this video. Please check the URL and try again.
        </Text>
        <TouchableOpacity
          onPress={onCancel}
          className="mt-6 bg-gray-200 py-3 px-6 rounded-lg"
        >
          <Text className="text-gray-800 font-medium">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 flex-col bg-white">
      <View className="px-5 pt-5">
        <View className="flex-row items-center mb-4">
          <TouchableOpacity onPress={onCancel} className="mr-3">
            <Icon
              name="arrow-left"
              type="font-awesome"
              size={20}
              color="#4B5563"
            />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">
            YouTube Video Information
          </Text>
        </View>
      </View>

      <ScrollView
        className="flex-1 px-5"
        contentContainerStyle={{ paddingBottom: 95 }} // Ensure content doesn't get hidden behind the buttons
      >
        {/* Main Video Info Card */}
        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4 shadow-sm">
          <Text className="text-lg font-bold text-gray-800 mb-3">{videoInfo.title}</Text>

          <View className="flex-row items-center mb-2">
            <Icon name="clock" type="font-awesome-5" size={16} color="#4B5563" />
            <Text className="ml-2 text-gray-700">
              Duration: {formatDuration(videoInfo.duration)}
            </Text>
          </View>


            <View className="flex-row items-start mb-2">
              <Icon name="file-contract" type="font-awesome-5" size={16} color="#4B5563" style={{marginTop: 2}} />
              <View className="ml-2 flex-1">
                <Text className="text-gray-700">
                  License: {videoInfo.license || "Unknown"}
                </Text>
                {videoInfo.license && (
                <Text className="text-xs text-gray-500">
                  {getLicenseDescription(videoInfo.license)}
                </Text>
                 )}
              </View>
            </View>


          {videoInfo.categories && videoInfo.categories.length > 0 && (
            <View className="flex-row items-start mb-2">
              <Icon name="folder" type="font-awesome" size={16} color="#4B5563" style={{marginTop: 2}} />
              <Text className="ml-2 text-gray-700">
                Categories: {videoInfo.categories.join(", ")}
              </Text>
            </View>
          )}

          <View className="flex-row items-center mb-2">
            <Icon
              name={videoInfo.age_limit > 0 ? "user-shield" : "user"}
              type="font-awesome-5"
              size={16}
              color={videoInfo.age_limit > 0 ? "#EF4444" : "#4B5563"}
            />
            <Text className="ml-2 text-gray-700">
              Age Limit: {videoInfo.age_limit > 0 ? `${videoInfo.age_limit}+` : "None"}
            </Text>
          </View>

          <View className="flex-row items-center mb-2">
            <Icon
              name={videoInfo.is_live ? "broadcast-tower" : "video"}
              type="font-awesome-5"
              size={16}
              color={videoInfo.is_live ? "#EF4444" : "#4B5563"}
            />
            <Text className="ml-2 text-gray-700">
              {videoInfo.is_live ? "Live Stream" : "Uploaded/Recorded Video"}
            </Text>
          </View>

          <View className="flex-row items-center mb-2">
            <Icon
              name="eye"
              type="font-awesome-5"
              size={16}
              color="#4B5563"
            />
            <Text className="ml-2 text-gray-700">
              Views: {videoInfo.view_count?.toLocaleString() || "Unknown"}
            </Text>
          </View>

          {/* License Warning */}
          <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2">
            <Text className="text-sm text-yellow-800 font-medium">
              ⚠️ Please check the license before using this content. Not all YouTube videos are free to reuse.
            </Text>
          </View>
        </View>

        {/* Expandable Section */}
        <View className="bg-white rounded-lg border border-gray-200 mb-4 shadow-sm overflow-hidden">
          <TouchableOpacity
            className="flex-row justify-between items-center p-4"
            onPress={() => setExpanded(!expanded)}
          >
            <Text className="font-medium text-gray-800">Additional Information</Text>
            <Icon
              name={expanded ? "chevron-up" : "chevron-down"}
              type="font-awesome"
              size={16}
              color="#4B5563"
            />
          </TouchableOpacity>

          {expanded && (
            <View className="p-4 pt-0 border-t border-gray-200">
              <View className="mt-3">
                <Text className="font-medium text-gray-700">Uploader</Text>
                <Text className="text-gray-600">{videoInfo.uploader || "Unknown"}</Text>
              </View>

              <View className="mt-3">
                <Text className="font-medium text-gray-700">Uploader ID</Text>
                <Text className="text-gray-600">{videoInfo.uploader_id || "Unknown"}</Text>
              </View>

              <View className="mt-3">
                <Text className="font-medium text-gray-700">Description</Text>
                <Text className="text-gray-600 mt-1">{videoInfo.description || "No description available"}</Text>
              </View>

              {videoInfo.tags && videoInfo.tags.length > 0 && (
                <View className="mt-3">
                  <Text className="font-medium text-gray-700">Tags</Text>
                  <View className="flex-row flex-wrap mt-1">
                    {videoInfo.tags.map((tag) => (
                      <View key={`tag-${tag}`} className="bg-gray-100 rounded-full px-3 py-1 mr-2 mb-2">
                        <Text className="text-xs text-gray-700">#{tag}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Action Buttons - Fixed at bottom using common styling */}
      <View
        className="bg-white border-t border-gray-200"
        style={
          buttonContainerStyle || {
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            padding: "12px 0", // Vertical padding only, matching COMMON_STYLES
            marginBottom: 0, // Position buttons right at the bottom, matching COMMON_STYLES
            backgroundColor: "white",
            borderTop: "1px solid #e5e7eb",
            zIndex: 10,
          }
        }
      >
        {isLongVideo ? (
          <TouchableOpacity
            onPress={onCancel}
            className="py-4 rounded-lg bg-red-500"
            style={{ marginHorizontal: 20 }}
          >
            <Text className="text-center text-white font-medium">
              Video longer than 1 hour. Select another video
            </Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            onPress={() => onSelectVideo(videoInfo)}
            className="py-4 rounded-lg bg-blue-600"
            style={{ marginHorizontal: 20 }}
          >
            <Text className="text-center text-white font-medium">
              Pick this video
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

YouTubeVideoInfoCard.propTypes = {
  videoUrl: PropTypes.string.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSelectVideo: PropTypes.func.isRequired,
  buttonContainerStyle: PropTypes.object,
};

YouTubeVideoInfoCard.defaultProps = {
  buttonContainerStyle: null,
};
