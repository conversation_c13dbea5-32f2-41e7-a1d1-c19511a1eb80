import React, { useState, useCallback, useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Platform,
  Dimensions
} from 'react-native';
import { Icon } from '@rneui/themed';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import axios from 'axios';
import Constants from 'expo-constants';
import PropTypes from 'prop-types';
import JobStatusCardWrapper from '../components/JobStatusCardWrapper';
import { UserContext } from '../context/UserContext';
import ResponsiveContainer from '../components/layout/ResponsiveContainer';
import ProfilePictureIcon from '../components/navigation/ProfilePictureIcon';


const windowWidth = Dimensions.get('window').width;
const isMobile = Platform.OS === 'android' || Platform.OS === 'ios' || windowWidth < 768;

const JobStatusScreen = () => {
  const navigation = useNavigation();
  const { user } = useContext(UserContext);
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');


  const fetchJobs = async () => {
    try {
      if (!user?.email) {
        console.log('User not authenticated, email missing');
        setError('User not authenticated');
        setLoading(false);
        return;
      }

      const serviceUrl = Constants.expoConfig?.extra?.SANATANA_JOBSTATUS_DOMAIN;
      if (!serviceUrl) {
        console.error('Job status service URL is undefined');
        setError('Job service configuration missing');
        setLoading(false);
        return;
      }

      console.log(`JobStatusScreen: Fetching jobs for user: ${user.email}`);
      setError(null);

      const url = `${serviceUrl}/api/jobs/user/${user.email}/pending`;
      console.log('JobStatusScreen: Fetching from URL:', url);

      const response = await axios.get(url);
      console.log('JobStatusScreen: Job fetch response:', response.status, typeof response.data);

      // Ensure jobs is always an array
      let jobsData = Array.isArray(response.data) ? response.data : [];
      console.log(`JobStatusScreen: Received ${jobsData.length} jobs`);

      // Ensure each job has a valid history array
      jobsData = jobsData.map(job => {
        // If job has no status object, create one
        if (!job.status) {
          job.status = {
            current: job.current || 'unknown',
            history: [],
            last_updated: new Date().toISOString()
          };
        }

        // If status is a string, convert to object
        if (typeof job.status === 'string') {
          job.status = {
            current: job.status,
            history: job.history || [],
            last_updated: job.last_updated || new Date().toISOString()
          };
        }

        // Ensure history is an array
        if (!Array.isArray(job.status.history)) {
          console.warn(`Job ${job.id} history is not an array, resetting to empty array`);
          job.status.history = [];
        }

        return job;
      });

      // Preserve history from existing jobs
      const updatedJobs = jobsData.map(newJob => {
        // Find the existing job with the same ID
        const existingJob = jobs.find(job => job.id === newJob.id);

        if (Array.isArray(existingJob?.status?.history)) {
          // If the existing job has history and the new job doesn't, use the existing history
          if (!newJob.status?.history?.length) {
            console.log(`Preserving history for job ${newJob.id} from existing data`);
            newJob.status.history = existingJob.status.history;
          }
          // If both have history but new job has fewer entries, something might be wrong
          else if (newJob.status?.history &&
                   existingJob.status.history.length > newJob.status.history.length) {
            console.warn(`New job ${newJob.id} has fewer history entries than existing job, preserving existing history`);
            newJob.status.history = existingJob.status.history;
          }
        }

        return newJob;
      });

      setJobs(updatedJobs);
    } catch (err) {
      console.error('JobStatusScreen: Error fetching jobs:', err);
      console.error('JobStatusScreen: Error details:', err.message, err.stack);
      setError(`Failed to load jobs: ${err.message}`);
      // Set jobs to empty array on error
      setJobs([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchJobs();
  }, []);

  // Fetch jobs when screen comes into focus and set up auto-refresh
  useFocusEffect(
    useCallback(() => {
      console.log('JobStatusScreen: Screen focused, fetching jobs...');

      // Initial fetch
      fetchJobs().catch(error => {
        console.error('JobStatusScreen: Error in initial fetchJobs:', error);
      });

      // Set up auto-refresh every 15 seconds
      let intervalId;
      try {
        intervalId = setInterval(() => {
          console.log('JobStatusScreen: Auto-refreshing job status...');
          fetchJobs().catch(refreshError => {
            console.error('JobStatusScreen: Error in auto-refresh fetchJobs:', refreshError);
          });
        }, 15000); // 15 seconds
      } catch (intervalError) {
        console.error('JobStatusScreen: Error setting up interval:', intervalError);
      }

      // Clean up interval when screen loses focus
      return () => {
        console.log('JobStatusScreen: Screen unfocused, cleaning up...');
        if (intervalId) {
          try {
            clearInterval(intervalId);
            console.log('JobStatusScreen: Interval cleared successfully');
          } catch (cleanupError) {
            console.error('JobStatusScreen: Error clearing interval:', cleanupError);
          }
        }
      };
    }, [user])
  );

  const handleStatusChange = (jobInfo) => {
    try {
      console.log(`JobStatusScreen: Handling status change for job ${jobInfo.id} to ${jobInfo.status?.current}`);
      // Update the job in the list with the full job info
      setJobs(prevJobs =>
        prevJobs.map(job =>
          job.id === jobInfo.id ? {
            ...job,
            status: jobInfo.status?.current,
            statusData: jobInfo.status, // Store the full status object
            lastUpdated: new Date().toISOString() // Add a timestamp to force re-render
          } : job
        )
      );
    } catch (error) {
      console.error('JobStatusScreen: Error handling status change:', error);
    }
  };

  const filteredJobs = statusFilter === 'all'
    ? jobs
    : jobs.filter(job => job.status === statusFilter);

  const renderStatusFilter = () => {
    // Simplified filter list for mobile
    const filters = isMobile
      ? [
          { id: 'all', label: 'All' },
          { id: 'queued', label: 'Queued' },
          { id: 'uploading', label: 'Uploading' },
          { id: 'completed', label: 'Completed' },
          { id: 'failed', label: 'Failed' },
        ]
      : [
          { id: 'all', label: 'All' },
          { id: 'created', label: 'Created' },
          { id: 'queued', label: 'Queued' },
          { id: 'downloading', label: 'Downloading' },
          { id: 'uploading', label: 'Uploading' },
          { id: 'completed', label: 'Completed' },
          { id: 'failed', label: 'Failed' },
          { id: 'scheduled', label: 'Limit Exceeded' },
          { id: 'retrying', label: 'Retrying' },
        ];

    return (
      <View className="bg-white border-b border-gray-200 p-3">
        <View className="flex-row items-center">
          <Text className="text-sm font-medium text-gray-600 mr-2">Filter:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row">
              {filters.map(item => (
                <TouchableOpacity
                  key={item.id}
                  className={`px-3 py-1.5 rounded-full mr-2 ${statusFilter === item.id ? 'bg-indigo-600' : 'bg-gray-100'}`}
                  onPress={() => setStatusFilter(item.id)}
                >
                  <Text
                    className={`text-sm font-medium ${statusFilter === item.id ? 'text-white' : 'text-gray-600'}`}
                  >
                    {item.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>
      </View>
    );
  };

  if (loading && !refreshing) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-50 p-4">
        <ActivityIndicator size="large" color="#4F46E5" />
        <Text className="text-base text-gray-600 mt-3">Loading jobs...</Text>
      </View>
    );
  }

  // Prepare content based on state
  let content;
  if (error) {
    content = (
      <View className="flex-1 justify-center items-center p-4">
        <Icon name="exclamation-circle" type="font-awesome" size={24} color="#EF4444" />
        <Text className="text-red-600 text-base mt-2 mb-4 text-center">{error}</Text>
        <TouchableOpacity
          className="bg-red-500 px-4 py-2 rounded-lg"
          onPress={fetchJobs}
        >
          <Text className="text-white font-medium">Retry</Text>
        </TouchableOpacity>
      </View>
    );
  } else if (filteredJobs.length === 0) {
    content = (
      <View className="flex-1 justify-center items-center p-4">
        <Icon name="inbox" type="font-awesome" size={48} color="#9CA3AF" />
        <Text className="text-gray-500 text-base mt-3 mb-4 text-center">
          {statusFilter === 'all'
            ? 'No upload jobs found'
            : `No ${statusFilter} jobs found`}
        </Text>
        <TouchableOpacity
          className="bg-indigo-600 px-4 py-2 rounded-lg"
          onPress={() => navigation.navigate('Home')}
        >
          <Text className="text-white font-medium">Create New Upload</Text>
        </TouchableOpacity>
      </View>
    );
  } else {
    content = (
      <View className="flex-1 w-full px-4 py-2">
        {filteredJobs.map(item => (
          <JobStatusCardWrapper
            key={item.id.toString()}
            jobId={item.id}
            onStatusChange={handleStatusChange}
          />
        ))}
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        className="flex-1"
      >
        <ResponsiveContainer>
          <View className="flex-row justify-between items-center p-4 bg-white border-b border-gray-200">
            <View className="flex-row items-center">
              <TouchableOpacity
                className="p-2 mr-2"
                onPress={() => navigation.navigate('ProfileMain')}
              >
                <ProfilePictureIcon size={28} />
              </TouchableOpacity>
              <Text className="text-xl font-bold text-gray-800">Upload Jobs</Text>
            </View>
            <TouchableOpacity
              className="p-2"
              onPress={onRefresh}
              disabled={refreshing}
            >
              {refreshing ? (
                <ActivityIndicator size="small" color="#4F46E5" />
              ) : (
                <Icon name="refresh" type="font-awesome" size={18} color="#4F46E5" />
              )}
            </TouchableOpacity>
          </View>

          {renderStatusFilter()}
          {content}
        </ResponsiveContainer>
      </ScrollView>
    </View>
  );
};

JobStatusScreen.propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired
  }).isRequired
};

export default JobStatusScreen;
